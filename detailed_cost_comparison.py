#!/usr/bin/env python3
"""
Detailed Cost Sheet Comparison Between Both Excel Files
"""

import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
import re
import os

def clean_product_name(name):
    """Clean and standardize product names for comparison"""
    if pd.isna(name):
        return ""

    name = str(name).strip().lower()
    # Remove common variations
    name = re.sub(r'\s*\([^)]*\)', '', name)  # Remove parentheses content
    name = re.sub(r'\s+', ' ', name)  # Normalize spaces
    name = name.replace('nos', '').replace('no', '').replace('.', '').strip()
    return name

def extract_cost_data_from_file(file_path):
    """Extract cost data from all relevant sheets in a file"""
    try:
        excel_file = pd.ExcelFile(file_path)
        print(f"\nAnalyzing: {file_path}")

        # Focus on main cost sheets
        target_sheets = ['Cost sheet', 'Cost Sheet']  # Different capitalizations

        all_data = []

        for sheet_name in excel_file.sheet_names:
            if any(target.lower() == sheet_name.lower() for target in target_sheets):
                print(f"Processing cost sheet: {sheet_name}")
                df = pd.read_excel(file_path, sheet_name=sheet_name)

                # Find header row
                header_row = None
                for idx, row in df.iterrows():
                    row_str = ' '.join([str(val) for val in row.values if pd.notna(val)]).lower()
                    if 'product' in row_str and ('cost' in row_str or 'mrp' in row_str):
                        header_row = idx
                        break

                if header_row is None:
                    continue

                # Extract structured data
                headers = df.iloc[header_row].values

                for idx in range(header_row + 1, len(df)):
                    row = df.iloc[idx]

                    if row.isna().all():
                        continue

                    # Find product name
                    product_name = None
                    for val in row.values:
                        if pd.notna(val) and isinstance(val, str) and len(str(val).strip()) > 2:
                            if not str(val).replace('.', '').replace(',', '').isdigit():
                                product_name = str(val).strip()
                                break

                    if not product_name:
                        continue

                    # Extract cost data
                    item_data = {
                        'Product_Original': product_name,
                        'Product_Clean': clean_product_name(product_name),
                        'Source_File': os.path.basename(file_path),
                        'Sheet_Name': sheet_name
                    }

                    # Map columns to standardized names
                    for i, header in enumerate(headers):
                        if pd.notna(header) and i < len(row):
                            header_str = str(header).lower().strip()
                            value = row.iloc[i] if i < len(row) else None

                            # Standardize column mappings
                            if any(term in header_str for term in ['qty', 'quantity']):
                                item_data['Quantity'] = value
                            elif 'unit' in header_str:
                                item_data['Unit'] = value
                            elif any(term in header_str for term in ['rm cost', 'raw material']):
                                item_data['RM_Cost'] = value
                            elif 'packing cost' in header_str or 'packing' in header_str:
                                item_data['Packing_Cost'] = value
                            elif 'labour cost' in header_str or 'labour' in header_str:
                                item_data['Labour_Cost'] = value
                            elif 'gas' in header_str:
                                item_data['Gas_Cost'] = value
                            elif 'tax' in header_str and '%' not in header_str:
                                item_data['Tax'] = value
                            elif 'mrp' in header_str and 'proposed' not in header_str:
                                item_data['MRP'] = value
                            elif 'supply' in header_str:
                                item_data['Supply_Rate'] = value
                            elif 'discount' in header_str and '%' not in header_str:
                                item_data['Discount_Amount'] = value
                            elif 'allocation' in header_str:
                                item_data['Allocation'] = value

                    all_data.append(item_data)

        return pd.DataFrame(all_data)

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return pd.DataFrame()

def compare_cost_sheets(df1, df2, file1_name, file2_name):
    """Compare cost sheets from two files"""

    # Create comparison dataframe
    comparison_data = []

    # Handle empty dataframes
    if df1.empty and df2.empty:
        return pd.DataFrame()

    # Get all unique products from both files
    products1 = df1['Product_Clean'].tolist() if not df1.empty and 'Product_Clean' in df1.columns else []
    products2 = df2['Product_Clean'].tolist() if not df2.empty and 'Product_Clean' in df2.columns else []
    all_products = set(products1 + products2)

    for product in all_products:
        if not product:  # Skip empty product names
            continue

        # Find product in both files
        product1 = df1[df1['Product_Clean'] == product]
        product2 = df2[df2['Product_Clean'] == product]

        comparison_item = {
            'Product_Clean': product,
            'Product_File1': product1['Product_Original'].iloc[0] if not product1.empty else None,
            'Product_File2': product2['Product_Original'].iloc[0] if not product2.empty else None,
            'Status': 'Unknown'
        }

        # Determine status
        if not product1.empty and not product2.empty:
            comparison_item['Status'] = 'Both Files'
        elif not product1.empty:
            comparison_item['Status'] = f'Only in {file1_name}'
        elif not product2.empty:
            comparison_item['Status'] = f'Only in {file2_name}'

        # Extract cost data from both files
        cost_fields = ['RM_Cost', 'Packing_Cost', 'Labour_Cost', 'Tax', 'MRP', 'Supply_Rate', 'Quantity']

        for field in cost_fields:
            # File 1 data
            val1 = product1[field].iloc[0] if not product1.empty and field in product1.columns else None
            comparison_item[f'{field}_File1'] = val1

            # File 2 data
            val2 = product2[field].iloc[0] if not product2.empty and field in product2.columns else None
            comparison_item[f'{field}_File2'] = val2

            # Calculate difference
            try:
                num_val1 = float(val1) if pd.notna(val1) else None
                num_val2 = float(val2) if pd.notna(val2) else None

                if num_val1 is not None and num_val2 is not None:
                    difference = num_val2 - num_val1
                    pct_difference = (difference / num_val1) * 100 if num_val1 != 0 else 0

                    comparison_item[f'{field}_Difference'] = difference
                    comparison_item[f'{field}_Pct_Difference'] = pct_difference
                else:
                    comparison_item[f'{field}_Difference'] = None
                    comparison_item[f'{field}_Pct_Difference'] = None
            except:
                comparison_item[f'{field}_Difference'] = None
                comparison_item[f'{field}_Pct_Difference'] = None

        comparison_data.append(comparison_item)

    return pd.DataFrame(comparison_data)

def create_highlighted_comparison_excel(comparison_df, file1_name, file2_name):
    """Create Excel with red highlights for differences"""

    wb = Workbook()
    ws = wb.active
    ws.title = "Cost Sheet Comparison"

    # Define styles
    red_fill = PatternFill(start_color="FF6B6B", end_color="FF6B6B", fill_type="solid")  # Bright red for major differences
    light_red_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")  # Light red for minor differences
    green_fill = PatternFill(start_color="51CF66", end_color="51CF66", fill_type="solid")  # Green for items in both files
    yellow_fill = PatternFill(start_color="FFE066", end_color="FFE066", fill_type="solid")  # Yellow for items in one file only

    bold_font = Font(bold=True)
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="2C3E50", end_color="2C3E50", fill_type="solid")

    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Add headers
    headers = list(comparison_df.columns)
    for col_idx, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_idx, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = thin_border
        cell.alignment = Alignment(horizontal='center', vertical='center')

    # Add data with conditional formatting
    for row_idx, (_, row) in enumerate(comparison_df.iterrows(), 2):
        for col_idx, (col_name, value) in enumerate(row.items(), 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.border = thin_border

            # Apply conditional formatting
            if col_name == 'Status':
                if 'Both Files' in str(value):
                    cell.fill = green_fill
                elif 'Only in' in str(value):
                    cell.fill = yellow_fill

            elif '_Pct_Difference' in col_name:
                try:
                    pct_val = float(value) if pd.notna(value) else 0
                    if abs(pct_val) > 50:  # >50% difference - bright red
                        cell.fill = red_fill
                        cell.font = Font(bold=True, color="FFFFFF")
                    elif abs(pct_val) > 20:  # >20% difference - light red
                        cell.fill = light_red_fill
                        cell.font = Font(bold=True)
                except:
                    pass

            elif '_Difference' in col_name and 'Pct' not in col_name:
                try:
                    diff_val = float(value) if pd.notna(value) else 0
                    if abs(diff_val) > 20:  # Significant absolute difference
                        cell.fill = light_red_fill
                except:
                    pass

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 30)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Add summary and insights sheet
    summary_ws = wb.create_sheet("Summary & Insights")

    # Calculate summary statistics
    total_products = len(comparison_df)
    both_files = len(comparison_df[comparison_df['Status'] == 'Both Files'])
    only_file1 = len(comparison_df[comparison_df['Status'].str.contains(file1_name.split('.')[0], na=False)])
    only_file2 = len(comparison_df[comparison_df['Status'].str.contains(file2_name.split('.')[0], na=False)])

    # Calculate significant differences
    significant_rm_diff = 0
    significant_mrp_diff = 0

    if 'RM_Cost_Pct_Difference' in comparison_df.columns:
        significant_rm_diff = len(comparison_df[abs(comparison_df['RM_Cost_Pct_Difference']) > 20])

    if 'MRP_Pct_Difference' in comparison_df.columns:
        significant_mrp_diff = len(comparison_df[abs(comparison_df['MRP_Pct_Difference']) > 20])

    summary_data = [
        ["COST SHEET COMPARISON ANALYSIS", ""],
        ["=" * 40, ""],
        ["", ""],
        ["OVERVIEW", ""],
        [f"File 1: {file1_name}", ""],
        [f"File 2: {file2_name}", ""],
        [f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ""],
        ["", ""],
        ["PRODUCT COVERAGE", ""],
        [f"Total Unique Products: {total_products}", ""],
        [f"Products in Both Files: {both_files}", f"({both_files/total_products*100:.1f}%)"],
        [f"Products Only in {file1_name}: {only_file1}", f"({only_file1/total_products*100:.1f}%)"],
        [f"Products Only in {file2_name}: {only_file2}", f"({only_file2/total_products*100:.1f}%)"],
        ["", ""],
        ["COST DIFFERENCES (for common products)", ""],
        [f"Significant RM Cost Differences (>20%): {significant_rm_diff}", ""],
        [f"Significant MRP Differences (>20%): {significant_mrp_diff}", ""],
        ["", ""],
        ["KEY INSIGHTS", ""],
        ["1. RED HIGHLIGHTS indicate major cost differences (>50%)", ""],
        ["2. LIGHT RED indicates moderate differences (>20%)", ""],
        ["3. YELLOW indicates products unique to one file", ""],
        ["4. GREEN indicates products present in both files", ""],
        ["", ""],
        ["RECOMMENDATIONS", ""],
        ["1. Investigate all RED highlighted differences immediately", ""],
        ["2. Review products unique to one file for data completeness", ""],
        ["3. Standardize costing methodology between systems", ""],
        ["4. Implement regular cost data reconciliation process", ""],
        ["5. Focus on high-value products with significant differences", ""],
        ["", ""],
        ["ACTION ITEMS", ""],
        ["□ Review cost calculation formulas in both systems", ""],
        ["□ Verify raw material cost sources", ""],
        ["□ Check for data entry errors in highlighted items", ""],
        ["□ Update pricing strategy based on cost differences", ""],
        ["□ Establish single source of truth for cost data", ""],
    ]

    # Add summary data to sheet
    for row_idx, (item, value) in enumerate(summary_data, 1):
        summary_ws.cell(row=row_idx, column=1, value=item)
        summary_ws.cell(row=row_idx, column=2, value=value)

        if any(keyword in str(item) for keyword in ["ANALYSIS", "OVERVIEW", "COVERAGE", "DIFFERENCES", "INSIGHTS", "RECOMMENDATIONS", "ACTION"]):
            summary_ws.cell(row=row_idx, column=1).font = bold_font

    # Auto-adjust summary sheet columns
    summary_ws.column_dimensions['A'].width = 50
    summary_ws.column_dimensions['B'].width = 20

    output_file = "Cost_Sheet_Comparison_With_Red_Highlights.xlsx"
    wb.save(output_file)
    print(f"\nDetailed comparison with red highlights saved to: {output_file}")

    return output_file

def main():
    """Main comparison function"""
    print("DETAILED COST SHEET COMPARISON")
    print("=" * 50)

    # Find specific Excel files (exclude generated analysis files)
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~')
                   and 'Analysis' not in f and 'Comparison' not in f and 'Summary' not in f]

    if len(excel_files) < 2:
        print(f"Found only {len(excel_files)} source Excel file(s). Need at least 2 for comparison.")
        if excel_files:
            print(f"Available source files: {excel_files}")
        return

    # Use the first two source Excel files found
    file1 = excel_files[0]
    file2 = excel_files[1]

    print(f"Comparing:")
    print(f"  File 1: {file1}")
    print(f"  File 2: {file2}")

    # Extract cost data from both files
    df1 = extract_cost_data_from_file(file1)
    df2 = extract_cost_data_from_file(file2)

    if df1.empty and df2.empty:
        print("No cost sheet data found in either file")
        return

    print(f"\nExtracted {len(df1)} cost items from {file1}")
    print(f"Extracted {len(df2)} cost items from {file2}")

    # Compare the data
    comparison = compare_cost_sheets(df1, df2, file1, file2)

    # Create highlighted Excel file
    output_file = create_highlighted_comparison_excel(comparison, file1, file2)

    # Print summary
    print(f"\nCOMPARISON SUMMARY:")
    print(f"Total products analyzed: {len(comparison)}")
    print(f"Products in both files: {len(comparison[comparison['Status'] == 'Both Files'])}")
    print(f"Products only in {file1}: {len(comparison[comparison['Status'].str.contains(file1.split('.')[0], na=False)])}")
    print(f"Products only in {file2}: {len(comparison[comparison['Status'].str.contains(file2.split('.')[0], na=False)])}")

    # Highlight major differences
    if 'RM_Cost_Pct_Difference' in comparison.columns:
        major_rm_diff = comparison[abs(comparison['RM_Cost_Pct_Difference']) > 50]
        if not major_rm_diff.empty:
            print(f"\n⚠️  MAJOR RM COST DIFFERENCES (>50%):")
            for _, row in major_rm_diff.head(5).iterrows():
                print(f"   {row['Product_File1'] or row['Product_File2']}: {row['RM_Cost_Pct_Difference']:.1f}%")

    print(f"\n✅ Analysis complete! Check {output_file} for detailed comparison with red highlights.")

if __name__ == "__main__":
    main()
