#!/usr/bin/env python3
"""
Detailed Cost Sheet Comparison with Highlighting and Insights
"""

import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import re

def clean_product_name(name):
    """Clean and standardize product names for comparison"""
    if pd.isna(name):
        return ""

    name = str(name).strip().lower()
    # Remove common variations
    name = re.sub(r'\s*\([^)]*\)', '', name)  # Remove parentheses content
    name = re.sub(r'\s+', ' ', name)  # Normalize spaces
    name = name.replace('nos', '').replace('no', '').replace('.', '').strip()
    return name

def extract_cost_sheet_data(file_path, sheet_name):
    """Extract and structure cost sheet data"""
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        print(f"Processing {file_path} - {sheet_name}")

        # Find the header row
        header_row = None
        for idx, row in df.iterrows():
            row_str = ' '.join([str(val) for val in row.values if pd.notna(val)]).lower()
            if 'product' in row_str and ('cost' in row_str or 'mrp' in row_str):
                header_row = idx
                break

        if header_row is None:
            print(f"No header found in {sheet_name}")
            return pd.DataFrame()

        # Extract headers
        headers = df.iloc[header_row].values

        # Create structured data
        structured_data = []

        for idx in range(header_row + 1, len(df)):
            row = df.iloc[idx]

            # Skip empty rows
            if row.isna().all():
                continue

            # Find product name (first meaningful text column)
            product_name = None
            for val in row.values:
                if pd.notna(val) and isinstance(val, str) and len(str(val).strip()) > 2:
                    if not str(val).replace('.', '').replace(',', '').isdigit():
                        product_name = str(val).strip()
                        break

            if not product_name:
                continue

            # Extract cost data
            item_data = {
                'Product_Original': product_name,
                'Product_Clean': clean_product_name(product_name),
                'Source_File': file_path.split('\\')[-1] if '\\' in file_path else file_path.split('/')[-1]
            }

            # Map columns to standardized names
            for i, header in enumerate(headers):
                if pd.notna(header) and i < len(row):
                    header_str = str(header).lower().strip()
                    value = row.iloc[i] if i < len(row) else None

                    # Standardize column mappings
                    if any(term in header_str for term in ['qty', 'quantity']):
                        item_data['Quantity'] = value
                    elif 'unit' in header_str:
                        item_data['Unit'] = value
                    elif any(term in header_str for term in ['rm cost', 'raw material']):
                        item_data['RM_Cost'] = value
                    elif 'packing cost' in header_str or 'packing' in header_str:
                        item_data['Packing_Cost'] = value
                    elif 'labour cost' in header_str or 'labour' in header_str:
                        item_data['Labour_Cost'] = value
                    elif 'gas' in header_str:
                        item_data['Gas_Cost'] = value
                    elif 'tax' in header_str and '%' not in header_str:
                        item_data['Tax'] = value
                    elif 'mrp' in header_str and 'proposed' not in header_str:
                        item_data['MRP'] = value
                    elif 'supply' in header_str:
                        item_data['Supply_Rate'] = value
                    elif 'discount' in header_str and '%' not in header_str:
                        item_data['Discount_Amount'] = value
                    elif 'allocation' in header_str:
                        item_data['Allocation'] = value

            structured_data.append(item_data)

        return pd.DataFrame(structured_data)

    except Exception as e:
        print(f"Error processing {file_path} - {sheet_name}: {e}")
        return pd.DataFrame()

def compare_cost_sheets(df1, df2, file1_name, file2_name):
    """Compare two cost sheet dataframes and identify differences"""

    # Merge on cleaned product names
    comparison = pd.merge(
        df1, df2,
        on='Product_Clean',
        how='outer',
        suffixes=(f'_{file1_name}', f'_{file2_name}')
    )

    # Identify comparison categories
    comparison['Comparison_Status'] = 'Unknown'
    comparison['Key_Differences'] = ''

    for idx, row in comparison.iterrows():
        file1_product = row.get(f'Product_Original_{file1_name}')
        file2_product = row.get(f'Product_Original_{file2_name}')

        if pd.notna(file1_product) and pd.notna(file2_product):
            comparison.at[idx, 'Comparison_Status'] = 'Both Files'
        elif pd.notna(file1_product):
            comparison.at[idx, 'Comparison_Status'] = f'Only in {file1_name}'
        elif pd.notna(file2_product):
            comparison.at[idx, 'Comparison_Status'] = f'Only in {file2_name}'

    # Calculate differences for numerical columns
    numerical_cols = ['RM_Cost', 'Packing_Cost', 'Labour_Cost', 'Tax', 'MRP', 'Supply_Rate']

    for col in numerical_cols:
        col1 = f'{col}_{file1_name}'
        col2 = f'{col}_{file2_name}'

        if col1 in comparison.columns and col2 in comparison.columns:
            # Calculate difference
            diff_col = f'{col}_Difference'
            comparison[diff_col] = pd.to_numeric(comparison[col2], errors='coerce') - pd.to_numeric(comparison[col1], errors='coerce')

            # Calculate percentage difference
            pct_diff_col = f'{col}_Pct_Difference'
            comparison[pct_diff_col] = (comparison[diff_col] / pd.to_numeric(comparison[col1], errors='coerce')) * 100

    return comparison

def create_highlighted_excel(comparison_df, output_file):
    """Create Excel file with conditional formatting and highlights"""

    wb = Workbook()
    ws = wb.active
    ws.title = "Cost Sheet Comparison"

    # Define styles
    red_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
    green_fill = PatternFill(start_color="CCFFCC", end_color="CCFFCC", fill_type="solid")
    yellow_fill = PatternFill(start_color="FFFFCC", end_color="FFFFCC", fill_type="solid")
    blue_fill = PatternFill(start_color="CCE5FF", end_color="CCE5FF", fill_type="solid")

    bold_font = Font(bold=True)
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Add headers
    headers = list(comparison_df.columns)
    for col_idx, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_idx, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = thin_border
        cell.alignment = Alignment(horizontal='center', vertical='center')

    # Add data with conditional formatting
    for row_idx, (_, row) in enumerate(comparison_df.iterrows(), 2):
        for col_idx, (col_name, value) in enumerate(row.items(), 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.border = thin_border

            # Apply conditional formatting based on content
            if 'Comparison_Status' in col_name:
                if 'Only in' in str(value):
                    cell.fill = red_fill
                elif 'Both Files' in str(value):
                    cell.fill = green_fill

            elif '_Difference' in col_name and 'Pct' not in col_name:
                if pd.notna(value) and value != 0:
                    if abs(float(value)) > 10:  # Significant difference
                        cell.fill = red_fill
                    elif abs(float(value)) > 5:  # Moderate difference
                        cell.fill = yellow_fill

            elif '_Pct_Difference' in col_name:
                if pd.notna(value) and value != 0:
                    if abs(float(value)) > 20:  # >20% difference
                        cell.fill = red_fill
                    elif abs(float(value)) > 10:  # >10% difference
                        cell.fill = yellow_fill

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Add summary sheet
    summary_ws = wb.create_sheet("Summary & Insights")

    # Calculate summary statistics
    total_products = len(comparison_df)
    both_files = len(comparison_df[comparison_df['Comparison_Status'] == 'Both Files'])
    only_file1 = len(comparison_df[comparison_df['Comparison_Status'].str.contains('Bakery costing', na=False)])
    only_file2 = len(comparison_df[comparison_df['Comparison_Status'].str.contains('GdwnSum', na=False)])

    summary_data = [
        ["COST SHEET COMPARISON SUMMARY", ""],
        ["", ""],
        ["Total Products Analyzed", total_products],
        ["Products in Both Files", both_files],
        ["Products Only in Bakery costing May 2025.xlsx", only_file1],
        ["Products Only in GdwnSum (1GtR).xlsx", only_file2],
        ["", ""],
        ["KEY INSIGHTS", ""],
        ["", ""],
        ["1. Product Coverage", ""],
        [f"   - {both_files} products appear in both files", ""],
        [f"   - {only_file1 + only_file2} products are unique to one file", ""],
        ["", ""],
        ["2. Cost Variations (for common products)", ""],
    ]

    # Add cost variation analysis
    if both_files > 0:
        common_products = comparison_df[comparison_df['Comparison_Status'] == 'Both Files']

        for cost_type in ['RM_Cost', 'MRP']:
            diff_col = f'{cost_type}_Difference'
            if diff_col in common_products.columns:
                differences = pd.to_numeric(common_products[diff_col], errors='coerce').dropna()
                if len(differences) > 0:
                    avg_diff = differences.mean()
                    max_diff = differences.max()
                    min_diff = differences.min()

                    summary_data.extend([
                        [f"   {cost_type.replace('_', ' ')} Differences:", ""],
                        [f"     - Average: {avg_diff:.2f}", ""],
                        [f"     - Maximum: {max_diff:.2f}", ""],
                        [f"     - Minimum: {min_diff:.2f}", ""],
                    ])

    summary_data.extend([
        ["", ""],
        ["3. Recommendations", ""],
        ["   - Review products with significant cost differences (highlighted in red)", ""],
        ["   - Investigate products unique to one file for completeness", ""],
        ["   - Standardize costing methodology across both systems", ""],
        ["   - Regular reconciliation of cost data between files", ""],
        ["", ""],
        ["COLOR CODING LEGEND", ""],
        ["Red: Significant differences or items in only one file", ""],
        ["Yellow: Moderate differences", ""],
        ["Green: Items present in both files", ""],
        ["Blue: Header information", ""],
    ])

    # Add summary data to sheet
    for row_idx, (item, value) in enumerate(summary_data, 1):
        summary_ws.cell(row=row_idx, column=1, value=item)
        summary_ws.cell(row=row_idx, column=2, value=value)

        if "SUMMARY" in str(item) or "INSIGHTS" in str(item) or "Recommendations" in str(item):
            summary_ws.cell(row=row_idx, column=1).font = bold_font
        elif "COLOR CODING" in str(item):
            summary_ws.cell(row=row_idx, column=1).font = bold_font

    # Auto-adjust summary sheet columns
    for column in summary_ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 80)
        summary_ws.column_dimensions[column_letter].width = adjusted_width

    wb.save(output_file)
    print(f"Highlighted comparison saved to: {output_file}")

def analyze_available_file():
    """Analyze the available Excel file and extract cost sheet information"""
    print("COST SHEET ANALYSIS")
    print("=" * 50)

    # Check what files are available
    import os
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx')]

    if not excel_files:
        print("No Excel files found in the current directory")
        return

    print(f"Found Excel files: {excel_files}")

    # Analyze the available file
    file_path = excel_files[0]  # Use the first Excel file found

    try:
        # Read all sheets to understand structure
        excel_file = pd.ExcelFile(file_path)
        print(f"\nAnalyzing: {file_path}")
        print(f"Available sheets: {excel_file.sheet_names}")

        # Look for cost-related sheets
        cost_sheets = []
        for sheet in excel_file.sheet_names:
            if any(term in sheet.lower() for term in ['cost', 'sheet', 'price', 'mrp']):
                cost_sheets.append(sheet)

        print(f"Cost-related sheets found: {cost_sheets}")

        # Extract data from each cost sheet
        all_data = []
        for sheet in cost_sheets:
            df = extract_cost_sheet_data(file_path, sheet)
            if not df.empty:
                df['Sheet_Name'] = sheet
                all_data.append(df)
                print(f"Extracted {len(df)} items from sheet: {sheet}")

        if all_data:
            # Combine all data
            combined_df = pd.concat(all_data, ignore_index=True)

            # Create analysis Excel file
            create_cost_analysis_excel(combined_df, file_path)

            print(f"\nAnalysis Summary:")
            print(f"Total products analyzed: {len(combined_df)}")
            print(f"Sheets processed: {len(cost_sheets)}")

            # Show some statistics
            if 'RM_Cost' in combined_df.columns:
                rm_costs = pd.to_numeric(combined_df['RM_Cost'], errors='coerce').dropna()
                if len(rm_costs) > 0:
                    print(f"RM Cost range: {rm_costs.min():.2f} - {rm_costs.max():.2f}")

            if 'MRP' in combined_df.columns:
                mrps = pd.to_numeric(combined_df['MRP'], errors='coerce').dropna()
                if len(mrps) > 0:
                    print(f"MRP range: {mrps.min():.2f} - {mrps.max():.2f}")

        else:
            print("No cost data could be extracted from the file")

    except Exception as e:
        print(f"Error analyzing file: {e}")

def create_cost_analysis_excel(df, source_file):
    """Create Excel analysis of cost data with highlights"""

    wb = Workbook()
    ws = wb.active
    ws.title = "Cost Analysis"

    # Define styles
    red_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
    green_fill = PatternFill(start_color="CCFFCC", end_color="CCFFCC", fill_type="solid")
    yellow_fill = PatternFill(start_color="FFFFCC", end_color="FFFFCC", fill_type="solid")

    bold_font = Font(bold=True)
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Add headers
    headers = list(df.columns)
    for col_idx, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_idx, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = thin_border
        cell.alignment = Alignment(horizontal='center', vertical='center')

    # Add data with conditional formatting
    for row_idx, (_, row) in enumerate(df.iterrows(), 2):
        for col_idx, (col_name, value) in enumerate(row.items(), 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.border = thin_border

            # Highlight high-cost items
            if col_name == 'RM_Cost':
                try:
                    cost_val = float(value) if pd.notna(value) else 0
                    if cost_val > 50:  # High RM cost
                        cell.fill = red_fill
                    elif cost_val > 25:  # Medium RM cost
                        cell.fill = yellow_fill
                except:
                    pass

            elif col_name == 'MRP':
                try:
                    mrp_val = float(value) if pd.notna(value) else 0
                    if mrp_val > 100:  # High MRP
                        cell.fill = green_fill
                except:
                    pass

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Add insights sheet
    insights_ws = wb.create_sheet("Cost Insights")

    # Calculate insights
    total_products = len(df)
    sheets_analyzed = df['Sheet_Name'].nunique() if 'Sheet_Name' in df.columns else 1

    insights_data = [
        ["COST SHEET ANALYSIS INSIGHTS", ""],
        ["", ""],
        [f"Source File: {source_file}", ""],
        [f"Total Products Analyzed: {total_products}", ""],
        [f"Sheets Processed: {sheets_analyzed}", ""],
        ["", ""],
        ["COST ANALYSIS", ""],
    ]

    # Add cost statistics
    if 'RM_Cost' in df.columns:
        rm_costs = pd.to_numeric(df['RM_Cost'], errors='coerce').dropna()
        if len(rm_costs) > 0:
            insights_data.extend([
                ["Raw Material Costs:", ""],
                [f"  Average: {rm_costs.mean():.2f}", ""],
                [f"  Minimum: {rm_costs.min():.2f}", ""],
                [f"  Maximum: {rm_costs.max():.2f}", ""],
                [f"  Standard Deviation: {rm_costs.std():.2f}", ""],
            ])

    if 'MRP' in df.columns:
        mrps = pd.to_numeric(df['MRP'], errors='coerce').dropna()
        if len(mrps) > 0:
            insights_data.extend([
                ["", ""],
                ["MRP Analysis:", ""],
                [f"  Average: {mrps.mean():.2f}", ""],
                [f"  Minimum: {mrps.min():.2f}", ""],
                [f"  Maximum: {mrps.max():.2f}", ""],
                [f"  Standard Deviation: {mrps.std():.2f}", ""],
            ])

    insights_data.extend([
        ["", ""],
        ["RECOMMENDATIONS", ""],
        ["1. Review high-cost items (highlighted in red)", ""],
        ["2. Analyze pricing strategy for high-MRP items (highlighted in green)", ""],
        ["3. Consider cost optimization for medium-cost items (highlighted in yellow)", ""],
        ["4. Standardize cost calculation methodology", ""],
        ["", ""],
        ["COLOR CODING", ""],
        ["Red: High raw material costs (>50)", ""],
        ["Yellow: Medium raw material costs (25-50)", ""],
        ["Green: High MRP items (>100)", ""],
    ])

    # Add insights to sheet
    for row_idx, (item, value) in enumerate(insights_data, 1):
        insights_ws.cell(row=row_idx, column=1, value=item)
        insights_ws.cell(row=row_idx, column=2, value=value)

        if any(keyword in str(item) for keyword in ["INSIGHTS", "ANALYSIS", "RECOMMENDATIONS", "COLOR CODING"]):
            insights_ws.cell(row=row_idx, column=1).font = bold_font

    # Auto-adjust insights sheet columns
    for column in insights_ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 80)
        insights_ws.column_dimensions[column_letter].width = adjusted_width

    output_file = "Cost_Sheet_Analysis_Highlighted.xlsx"
    wb.save(output_file)
    print(f"Cost analysis saved to: {output_file}")

def main():
    """Main function"""
    analyze_available_file()

if __name__ == "__main__":
    main()
